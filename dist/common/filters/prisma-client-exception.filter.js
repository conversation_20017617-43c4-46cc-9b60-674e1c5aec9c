"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaClientExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const prisma_1 = require("../../../generated/prisma");
const core_1 = require("@nestjs/core");
const logger_service_1 = require("../logger/logger.service");
let PrismaClientExceptionFilter = class PrismaClientExceptionFilter {
    constructor(httpAdapterHost, logger) {
        this.httpAdapterHost = httpAdapterHost;
        this.logger = logger;
    }
    catch(exception, host) {
        this.logger.error('--- PRISMA CLIENT EXCEPTION FILTER CAUGHT AN ERROR ---');
        this.logger.error('Caught Exception:', JSON.stringify(exception, null, 2));
        const { httpAdapter } = this.httpAdapterHost;
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const correlationId = request.correlationId;
        let statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'An unexpected internal server error occurred.';
        let errorType = 'InternalServerError';
        let errorDetails = {};
        if (exception instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
            errorType = 'DatabaseError';
            switch (exception.code) {
                case 'P2000':
                    statusCode = common_1.HttpStatus.BAD_REQUEST;
                    message = `The provided value for the field '${exception.meta?.target}' is too long.`;
                    errorDetails = { field: exception.meta?.target, reason: 'value_too_long' };
                    break;
                case 'P2002':
                    statusCode = common_1.HttpStatus.CONFLICT;
                    message = `A record with this '${exception.meta?.target?.join(', ')}' already exists.`;
                    errorDetails = { fields: exception.meta?.target, reason: 'unique_constraint_failed' };
                    break;
                case 'P2003':
                    statusCode = common_1.HttpStatus.CONFLICT;
                    message = `A foreign key constraint failed on the field '${exception.meta?.field_name}'. The related record may not exist or the operation is not allowed.`;
                    errorDetails = { field: exception.meta?.field_name, reason: 'foreign_key_constraint_failed' };
                    break;
                case 'P2014':
                    statusCode = common_1.HttpStatus.CONFLICT;
                    message = `The change you are trying to make would violate the required relation '${exception.meta?.relation_name}' between the '${exception.meta?.model_a_name}' and '${exception.meta?.model_b_name}' models.`;
                    errorDetails = {
                        relation: exception.meta?.relation_name,
                        models: [exception.meta?.model_a_name, exception.meta?.model_b_name],
                        reason: 'relation_violation'
                    };
                    break;
                case 'P2025':
                    statusCode = common_1.HttpStatus.NOT_FOUND;
                    message = 'The requested resource or a related required resource was not found.';
                    errorDetails = { cause: exception.meta?.cause || 'Required record not found.', reason: 'record_not_found' };
                    break;
                default:
                    this.logger.logError(exception, {
                        correlationId,
                        errorCode: exception.code,
                        errorMeta: exception.meta,
                        type: 'prisma_unknown_error',
                    });
                    message = `A database error occurred (Code: ${exception.code}). Please check server logs.`;
                    errorDetails = { code: exception.code };
                    break;
            }
        }
        else if (exception instanceof prisma_1.Prisma.PrismaClientValidationError) {
            statusCode = common_1.HttpStatus.BAD_REQUEST;
            message = 'Input validation failed. Please check your data and try again.';
            errorType = 'ValidationError';
            this.logger.logError(exception, {
                correlationId,
                type: 'prisma_validation_error',
            });
            errorDetails = { reason: "Invalid input data based on schema." };
        }
        else {
            this.logger.logError(exception, {
                correlationId,
                type: 'prisma_unhandled_error',
            });
        }
        const responseBody = {
            statusCode,
            message,
            error: errorType === 'InternalServerError' && statusCode === common_1.HttpStatus.INTERNAL_SERVER_ERROR ? 'Internal Server Error' : errorType,
            details: errorDetails,
            timestamp: new Date().toISOString(),
            path: request.url,
        };
        httpAdapter.reply(response, responseBody, statusCode);
    }
};
exports.PrismaClientExceptionFilter = PrismaClientExceptionFilter;
exports.PrismaClientExceptionFilter = PrismaClientExceptionFilter = __decorate([
    (0, common_1.Catch)(prisma_1.Prisma.PrismaClientKnownRequestError, prisma_1.Prisma.PrismaClientValidationError),
    __metadata("design:paramtypes", [core_1.HttpAdapterHost,
        logger_service_1.AppLoggerService])
], PrismaClientExceptionFilter);
//# sourceMappingURL=prisma-client-exception.filter.js.map