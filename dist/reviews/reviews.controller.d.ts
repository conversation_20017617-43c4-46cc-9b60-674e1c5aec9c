import { ReviewsService } from './reviews.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { ListReviewsDto } from './dto/list-reviews.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { User as UserModel } from '../../generated/prisma';
export declare class ReviewsController {
    private readonly reviewsService;
    constructor(reviewsService: ReviewsService);
    submitReview(entityId: string, user: UserModel, createReviewDto: CreateReviewDto): Promise<{
        id: string;
        status: import("../../generated/prisma").$Enums.ReviewStatus;
        createdAt: Date;
        updatedAt: Date;
        content: string | null;
        title: string | null;
        userId: string;
        entityId: string;
        rating: number;
        moderationNotes: string | null;
        downvotes: number;
        moderatorId: string | null;
        upvotes: number;
    }>;
    getApprovedReviewsForEntity(entityId: string, listReviewsDto: ListReviewsDto): Promise<{
        data: Partial<import("../../generated/prisma").Review & {
            user: {
                username: string | null;
                profilePictureUrl: string | null;
                id: string;
            };
        }>[];
        total: number;
        page: number;
        limit: number;
    }>;
    updateUserReview(reviewId: string, user: UserModel, updateReviewDto: UpdateReviewDto): Promise<{
        id: string;
        status: import("../../generated/prisma").$Enums.ReviewStatus;
        createdAt: Date;
        updatedAt: Date;
        content: string | null;
        title: string | null;
        userId: string;
        entityId: string;
        rating: number;
        moderationNotes: string | null;
        downvotes: number;
        moderatorId: string | null;
        upvotes: number;
    }>;
    deleteUserReview(reviewId: string, user: UserModel): Promise<void>;
}
