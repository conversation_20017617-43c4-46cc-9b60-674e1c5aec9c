{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAE/C,gEAA4D;AAyBrD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACmB,aAA4B,EAC5B,aAA4B;QAE7C,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QACD,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,SAAS;SACvB,CAAC,CAAC;QAXc,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;IAW/C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAA2B;QACxC,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,gDAAgD,UAAU,EAAE,CAAC,CAAC;YAE1E,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;gBACrE,MAAM,IAAI,8BAAqB,CAAC,yCAAyC,CAAC,CAAC;YAC7E,CAAC;YAGD,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;YACpE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC3D,KAAK,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE;aAClC,CAAC,CAAC;YAEH,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,0DAA0D,UAAU,sBAAsB,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1H,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,8DAA8D,UAAU,kCAAkC,CAAC,CAAC;YAC1H,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;YAC3E,OAAO;gBACL,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,WAAW;gBACtB,iBAAiB,EAAE,CAAC,CAAC,WAAW;aACjC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAC5D,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACvC,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAtDY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGuB,sBAAa;QACb,8BAAa;GAHpC,WAAW,CAsDvB"}