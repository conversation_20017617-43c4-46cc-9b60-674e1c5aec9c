"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../../prisma/prisma.service");
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(configService, prismaService) {
        const jwtSecret = configService.get('JWT_SECRET');
        if (!jwtSecret) {
            throw new Error('JWT_SECRET environment variable is not set.');
        }
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: jwtSecret,
        });
        this.configService = configService;
        this.prismaService = prismaService;
    }
    async validate(payload) {
        console.log('--- JWT STRATEGY VALIDATE METHOD ENTERED ---');
        console.log('JWT Payload received:', JSON.stringify(payload, null, 2));
        try {
            const authUserId = payload.sub;
            console.log(`[JwtStrategy] Validating JWT for authUserId: ${authUserId}`);
            if (!authUserId) {
                console.error('[JwtStrategy] Invalid token: Missing subject (sub).');
                throw new common_1.UnauthorizedException('Invalid token: missing user identifier.');
            }
            console.log(`[JwtStrategy] Attempting to find user in database...`);
            const userProfile = await this.prismaService.user.findUnique({
                where: { authUserId: authUserId },
            });
            if (userProfile) {
                console.log(`[JwtStrategy] User profile found in DB for authUserId: ${authUserId}, public.users ID: ${userProfile.id}`);
            }
            else {
                console.log(`[JwtStrategy] User profile NOT found in DB for authUserId: ${authUserId}. This is okay for initial sync.`);
            }
            console.log('--- JWT STRATEGY VALIDATE METHOD COMPLETED SUCCESSFULLY ---');
            return {
                authData: payload,
                dbProfile: userProfile,
                profileExistsInDb: !!userProfile,
            };
        }
        catch (error) {
            console.error('--- JWT STRATEGY VALIDATE METHOD ERROR ---');
            console.error('Error details:', error);
            console.error('Error stack:', error.stack);
            throw error;
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService])
], JwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map