import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    console.log('--- JWT AUTH GUARD ACTIVATED ---');
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;
    console.log('Authorization header:', authHeader ? 'Present' : 'Missing');

    return super.canActivate(context);
  }

  handleRequest(err, user, info, context: ExecutionContext) {
    console.log('--- JWT AUTH GUARD HANDLE REQUEST ---');
    console.log('Error:', err);
    console.log('User:', user ? 'Present' : 'Missing');
    console.log('Info:', info);

    if (err || !user) {
      console.error('JwtAuthGuard Error:', err, 'Info:', info);
      throw err || new UnauthorizedException(info?.message || 'User is not authenticated');
    }

    console.log('--- JWT AUTH GUARD SUCCESS ---');
    return user;
  }
}